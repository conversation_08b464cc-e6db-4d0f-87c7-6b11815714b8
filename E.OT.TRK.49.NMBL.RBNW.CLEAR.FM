[E.OT.TRK.49.NMBL.RBNW.CLEAR.FM
 For open loan types 71, 73 and 95, 
 clear out the NMBL and RBNW fields of
 tracking 49 for the loan level. Also, 
 account level and external loan tracking
 level if they exist. 
  
  Al Zehrung 05/30/2025 IS-5626
]
TARGET=ACCOUNT

DEFINE
 TEMPNMBL=CHARACTER
 TEMPRBNW=CHARACTER
END

SELECT
 ACCOUNT:CLOSEDATE='--/--/--'
END

PRINT TITLE="DNI-TRACK 49 CLEAR NMBL RBNW FM"
 HEADERS END
  
 [CHECK FOR LOAN LEVEL TRACKING 49'S AND <PERSON>LEAR OUT THEIR NMBL AND RBNW FIELDS]  
 CALL CLEARVARS
 FOR EACH LOAN WITH ((LOAN:TYPE=71 AND LOAN:CLOSEDATE='--/--/-- ')OR
                     (LOAN:TYPE=73 AND LOAN:CLOSEDATE='--/--/-- ')OR
                     (LOAN:TYPE=95 AND LOAN:CLOSEDATE='--/--/-- '))
  DO
   FOR EACH LOAN TRACKING WITH LOAN TRACKING:TYPE=49
    DO
     IF LOAN TRACKING:USERCHAR6="Y" THEN TEMPNMBL=LOAN TRACKING:USERCHAR6
     IF LOAN TRACKING:USERCHAR7="Y" THEN TEMPRBNW=LOAN TRACKING:USERCHAR7
     IF (TEMPNMBL<>"" OR TEMPRBNW<>"") THEN
      DO       
       PRINT "ACCOUNT "+ACCOUNT:NUMBER+" MODIFY LOAN "+LOAN TRACKING:ID+" TRACKING LOC "
       PRINT LOAN TRACKING:LOCATOR
       NEWLINE       
       CALL CLEARNMBLRBNWFM
      END
    END
  END
  
 CALL CLEARVARS
 [CHECK FOR ACCOUNT LEVEL TRACKING 49'S AND CLEAR OUT THEIR NMBL AND RBNW FIELDS]
 FOR EACH TRACKING WITH (TRACKING:TYPE=49 AND UPPERCASE(TRACKING:USERCHAR8)="1-4 FAM")
  DO
   IF TRACKING:USERCHAR6="Y" THEN TEMPNMBL=TRACKING:USERCHAR6
   IF TRACKING:USERCHAR7="Y" THEN TEMPRBNW=TRACKING:USERCHAR7
   IF (TEMPNMBL<>"" OR TEMPRBNW<>"") THEN
    DO
     PRINT "ACCOUNT "+ACCOUNT:NUMBER+" MODIFY TRACKING LOC "
     PRINT TRACKING:LOCATOR
     NEWLINE
     CALL CLEARNMBLRBNWFM
    END
  END
  
  [CHECK FOR EXTERNAL LOAN LEVEL TRACKING 49'S AND CLEAR OUT THEIR NMBL AND RBNW FIELDS]  
  CALL CLEARVARS  
  FOR EACH EXTERNALLOAN WITH EXTERNALLOAN:CLOSEDATE='--/--/-- '
   DO
    FOR EACH EXTERNALLOAN TRACKING WITH (EXTERNALLOAN TRACKING:TYPE=49 AND UPPERCASE(EXTERNALLOAN TRACKING:USERCHAR8)="1-4 FAM")
     DO
      IF EXTERNALLOAN TRACKING:USERCHAR6="Y" THEN TEMPNMBL=EXTERNALLOAN TRACKING:USERCHAR6
      IF EXTERNALLOAN TRACKING:USERCHAR7="Y" THEN TEMPRBNW=EXTERNALLOAN TRACKING:USERCHAR7
      IF (TEMPNMBL<>"" OR TEMPRBNW<>"") THEN
       DO        
        PRINT "ACCOUNT "+ACCOUNT:NUMBER+" MODIFY EXTERNALLOAN LOC "
        PRINT EXTERNALLOAN:LOCATOR
        PRINT " TRACKING LOC "
        PRINT EXTERNALLOAN TRACKING:LOCATOR
        NEWLINE
        CALL CLEARNMBLRBNWFM
       END
     END
   END
END

PROCEDURE CLEARVARS
 TEMPNMBL=""
 TEMPRBNW=""
END

PROCEDURE CLEARNMBLRBNWFM
 IF TEMPNMBL="Y" THEN
  DO
   PRINT " CHANGE USERCHAR6 FROM" [NMBL]
   COL=49 TEMPNMBL
   COL=90 "TO"
   NEWLINE
  END
 IF TEMPRBNW="Y" THEN
  DO
   PRINT " CHANGE USERCHAR7 FROM" [RBNW]
   COL=49 TEMPRBNW
   COL=90 "TO"
   NEWLINE
  END
END
