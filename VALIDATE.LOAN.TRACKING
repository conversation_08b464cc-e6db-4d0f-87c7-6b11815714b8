[VALIDATE.LOAN.TRACKING

 DESIGNED TO PREVENT MODIFICATION OF LOAN TRACKING TYPE 44

 JOE KNAPTON - 8/30/2019

 Restrict FM revision to loan tracking 34 fields after
 Tenemos has completed its input:
 INSURANCECARRIER - LOAN TRACKING:USERCHAR12=82
 AGENTPHONE - LOAN TRACKING:USERCHAR13=83
 POLICYNUMBER - LOAN TRACKING:USERCHAR14=84

 Only user group 132 - VP, Lending are allowed to
 make after changes.

 AL ZEHRUNG - 12/28/2020
 
 Updated to produce a popup if user 
 enters a value that isn't first or
 junior into the lien position field.
 AL ZEHRUNG - 4/4/2023 - IS-2194
 
 Updated to add validation to the NMBL and RBNW
 fields in the tracking 49, they must remain blank
 
 Al Zehrung 6/3/2025 - IS-5626]

VALIDATION

TARGET=LOAN TRACKING

DEFINE
 #INCLUDE "RD.GETDATA.DEF"
 #INCLUDE "RD.GETFIELD.DEF"
 #INCLUDE "E.DEF.VALIDATE"
 TRUE=1
 FALSE=0
END

SETUP 
 IF @VALIDATEFIELDNUMBER=0 AND (<PERSON>OA<PERSON> TRACKING:TYPE=44)
    [AND SYSUSERNUMBER<>253] THEN [ANY FIELDS]
  DO
   @VALIDATEERROR="No FM allowed; for statements only."
  END

 IF LOAN TRACKING:TYPE=34 THEN
  DO
   IF GETDATANUMBER(GETUSERPRIVILEGEGROUP,SYSUSERNUMBER,132)=FALSE THEN
    DO
     IF @VALIDATEFIELDNUMBER=82 AND LOAN TRACKING:USERCHAR12<>"" THEN
      @VALIDATEERROR="No FM Allowed After Temenos Input"
     ELSE IF @VALIDATEFIELDNUMBER=83 AND LOAN TRACKING:USERCHAR13<>"" THEN
      @VALIDATEERROR="No FM Allowed After Temenos Input"
     ELSE IF @VALIDATEFIELDNUMBER=84 AND LOAN TRACKING:USERCHAR14<>"" THEN
      @VALIDATEERROR="No FM Allowed After Temenos Input"
    END
  END
 
 [VALIDATE TRACKING 49 AND THE CHARACTER DATA WITHIN THE LIEN POSITION MUST BE FIRST OR JUNIOR]
 IF @VALIDATEFMTYPE=CREATION OR @VALIDATEFMTYPE=REVISION THEN
  DO  
   IF LOAN TRACKING:TYPE=49 THEN
    DO
     IF @VALIDATEFIELDNUMBER=85 THEN
      DO
       IF UPPERCASE(@VALIDATECHARACTERINPUT)<>"FIRST" AND 
          UPPERCASE(@VALIDATECHARACTERINPUT)<>"JUNIOR" THEN @VALIDATEERROR="Allowed values must be First or Junior" 
      END
     IF (@VALIDATEFIELDNUMBER=25 OR @VALIDATEFIELDNUMBER=26) THEN [NMBL and RBNW must remain blank]
      DO
       IF @VALIDATECHARACTERINPUT<>"" AND UPPERCASE(LOAN TRACKING:USERCHAR8)="1-4 FAM" THEN @VALIDATEERROR="NMBL and RBNW must remain blank!" 
      END 
     IF @VALIDATEFIELDNUMBER=999 AND UPPERCASE(LOAN TRACKING:USERCHAR8)="1-4 FAM" AND (LOAN TRACKING:USERCHAR7<>"" OR LOAN TRACKING:USERCHAR6<>"") THEN 
      @VALIDATEERROR="NMBL and RBNW must remain blank!"
    END
  END
END

PRINT TITLE="VALIDATE.LOAN.TRACKING"
 SUPPRESSNEWLINE
END

#INCLUDE "E.PRO.VALIDATE"
