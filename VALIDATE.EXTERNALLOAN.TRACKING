[VALIDATE.EXTERNAL.LOAN.TRACKING

 Created to house the external loan 
 tracking validation information

 Updated to produce a popup if user 
 enters a value that isn't first or
 junior into the lien position field.
 AL ZEHRUNG - 4/4/2023 - IS-2194
 
 Updated to add validation to the NMBL and RBNW
 fields in the tracking 49, they must remain blank
 
 Al Zehrung 6/3/2025 - IS-5626
 ]

VALIDATION

TARGET=EXTERNALLOAN TRACKING

DEFINE
 #INCLUDE "RD.GETDATA.DEF"
 #INCLUDE "RD.GETFIELD.DEF"
 #INCLUDE "E.DEF.VALIDATE"
 TRUE=1
 FALSE=0
END

SETUP
 [VALIDATE TRACKING 49 AND THE CHARACTER DATA WITHIN THE LIEN POSITION MUST BE FIRST OR JUNIOR]
 IF @VALIDATEFMTYPE=CREATION OR @VALIDATEFMTYPE=REVISION THEN
  DO  
   IF EXTERNALLOAN TRACKING:TYPE=49 THEN
    DO
     IF @VALIDATEFIELDNUMBER=85 THEN
      DO
       IF UPPERCASE(@VALIDATECHARACTERINPUT)<>"FIRST" AND 
          UPPERCASE(@VALIDATECHARACTERINPUT)<>"JUNIOR" THEN @VALIDATEERROR="Allowed values must be First or Junior" 
      END
     IF (@VALIDATEFIELDNUMBER=25 OR @VALIDATEFIELDNUMBER=26) THEN [NMBL and RBNW must remain blank]
      DO
       IF @VALIDATECHARACTERINPUT<>"" AND UPPERCASE(EXTERNALLOAN TRACKING:USERCHAR8)="1-4 FAM" THEN @VALIDATEERROR="NMBL and RBNW must remain blank!" 
      END   
     IF @VALIDATEFIELDNUMBER=999 AND UPPERCASE(EXTERNALLOAN TRACKING:USERCHAR8)="1-4 FAM" AND (EXTERNALLOAN TRACKING:USERCHAR7<>"" OR EXTERNALLOAN TRACKING:USERCHAR6<>"") THEN 
      @VALIDATEERROR="NMBL and RBNW must remain blank!"    
    END  
  END
END

PRINT TITLE="VALIDATE.EXTERNAL.LOAN.TRACKING"
 SUPPRESSNEWLINE
END

#INCLUDE "E.PRO.VALIDATE"
