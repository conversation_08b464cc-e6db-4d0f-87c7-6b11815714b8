[VALIDATE.TRACKING

 DESIGNED TO PREVENT MODIFICATION OF TRACKING TYPE 79
 
 JOE KNAPTON - 6/14/2016
 
 Updated to produce a popup if user 
 enters a value that isn't first or
 junior into the lien position field.
 AL ZEHRUNG - 4/4/2023 - IS-2194
 
 Updated to add validation to the NMBL and RBNW
 fields in the tracking 49, they must remain blank
 
 Al Zehrung 6/3/2025 - IS-5626]
 
VALIDATION

TARGET=TRACKING

DEFINE
 #INCLUDE "RD.GETDATA.DEF"
 #INCLUDE "RD.GETFIELD.DEF"
 #INCLUDE "E.DEF.VALIDATE"
 TRUE=1
 FALSE=0
END

SETUP
 IF @VALIDATEFIELDNUMBER=0 AND (TRACKING:TYPE=76 OR
                                TRACKING:TYPE=79 OR
                                TRACKING:TYPE=90) AND 
    SYSUSERNUMBER<>254 AND
    SYSUSERNUMBER<>1607 AND
    SYSUSERNUMBER<>1612 AND
    SYSUSERNUMBER<>1620 AND
    SY<PERSON><PERSON><PERSON><PERSON>MBER<>1638 THEN [ANY FIELDS]
  DO
   IF TRACKING:TYPE=76 OR
      TRACKING:TYPE=79 THEN @VALIDATEERROR="Manual FM not allowed; Use the Referral dropdown!"
   IF TRACKING:TYPE=90 THEN @VALIDATEERROR="No FM allowed; values updated nightly."
  END
  
 ELSE IF @VALIDATEFIELDNUMBER=0 AND TRACKING:TYPE=51 AND
         SYSUSERNUMBER<>1612 THEN
  DO
   POPUPMESSAGE(1,"Manual FM not allowed. This tracking record is controlled through nightly processing.")
   @VALIDATEERROR="Error!"
  END

 ELSE IF @VALIDATEFIELDNUMBER=0 AND TRACKING:TYPE=82 AND
         GETDATANUMBER(GETUSERPRIVILEGEGROUP,SYSUSERNUMBER,236)=FALSE THEN
  DO
   POPUPMESSAGE(1,"Manual FM not allowed. Contact a Zelle help group member to update values.")
   @VALIDATEERROR="Error!"
  END

 [VALIDATE TRACKING 49 AND THE CHARACTER DATA WITHIN THE LIEN POSITION MUST BE FIRST OR JUNIOR]
 IF @VALIDATEFMTYPE=CREATION OR @VALIDATEFMTYPE=REVISION THEN
  DO  
   IF TRACKING:TYPE=49 THEN
    DO
     IF @VALIDATEFIELDNUMBER=85 THEN
      DO
       IF UPPERCASE(@VALIDATECHARACTERINPUT)<>"FIRST" AND 
          UPPERCASE(@VALIDATECHARACTERINPUT)<>"JUNIOR" THEN @VALIDATEERROR="Allowed values must be First or Junior" 
      END
     IF (@VALIDATEFIELDNUMBER=25 OR @VALIDATEFIELDNUMBER=26) THEN [NMBL and RBNW must remain blank]
      DO
       IF @VALIDATECHARACTERINPUT<>"" AND UPPERCASE(TRACKING:USERCHAR8)="1-4 FAM" THEN @VALIDATEERROR="NMBL and RBNW must remain blank!" 
      END     
     IF @VALIDATEFIELDNUMBER=999 AND UPPERCASE(TRACKING:USERCHAR8)="1-4 FAM" AND (TRACKING:USERCHAR7<>"" OR TRACKING:USERCHAR6<>"") THEN 
      @VALIDATEERROR="NMBL and RBNW must remain blank!"
    END
  END
END

PRINT TITLE="VALIDATE.TRACKING"
 SUPPRESSNEWLINE
END

#INCLUDE "E.PRO.VALIDATE"
