[
 SymForm PDF Driver        - PDF.EPISYSCHGADDRESS.119
 Auto generated variables  - PDF.EPISYSCHGADDRESS.119.DEF
 Auto generated procedures - PDF.EPISYSCHGADDRESS.119.PRO
 PDF Form filename         - PDF.EPISYSCHGADDRESS.119.pdf

 Author:   <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
 Created:  6/30/2015 2:14:58 PM 
 Version:  SymPDFCreator *******

 REP GEN THAT WILL REPLACE THE CURRENT CHANGE OF ADDRESS
 SYSTEM

 PER REQUEST OF MANY PEOPLE (LINDA H, MICHELLE G, GLENN B,
 ANNA R, DIANE D, ETC.)

 JOE KNAPTON - 3/10/2015
 
 ADDED ID CRITERIA PER REQUEST OF RACHEL L
 JOE KNAPTON - 12/11/2015
 
 ADDED ABILITY TO FM ADDRESS ON OTHER NAMES ON SAME ACCOUNT
 JEFF HARTMAN - 7/15/2020
 
 Added a popup warning if attempting to changes a physical address 
 to a PO Box
 
 Al Zehrung 12/30/2021 IS-137
 
 Added TEMPNAME and while loop in case there are more than one ampersand per name.
 Al Zehrung 5/2/2022 IS-1103
 
 Commented out section that excludes beneficiaries from appearing in 
 address change dialog and automatically updating. Beneficiaries can
 be chosen from the list with the other names to be updated.  
 
 Al Zehrung 5/2/2022 IS-988
 
 Future-proof additional name types, and pull name type definitions from parameters
 Jira IS-1616
 Joe Knapton - 9/7/2022
 
 Joe K Added in Name loop to build a list of unique accounts that have a NAME record 
 matching the given SSN as well as removing duplicates. Commented out array of finding 
 and processing any sub-record names on an account that match a specific SSN if the 
 base account-level name record exists.
 
 Al Zehrung 05/08/2025 IS-3247
 
 Adjusted the counts within the name dialog and FM to only apply to exact accounts
 
 Al Zehrung 05/22/2025 IS-5555
 ]

SUBROUTINE DEMAND WINDOWS

TARGET = ACCOUNT

DEFINE
 DEBUGLEVEL=0 [ADJUST THIS TO SEE VARIOUS POP-UPS THROUGHOUT THE PROGRAM; LEVEL OF 0 IS FOR NORMAL OPERATION]
 WHILECOUNTER=NUMBER [TO ALLOW AN INFINITE LOOP POP UP TO OCCUR, BUT LESS THAN 100,000 TIMES]

 #INCLUDE "RD.WINDOWS.DEF"
 #INCLUDE "RD.GETDATA.DEF"
 #INCLUDE "SYMPDF.DEF"
 #INCLUDE "PDF.EPISYSCHGADDRESS.119.DEF"
 #INCLUDE "ECU.DESCRIPTIONS.OD.DEF"

 ACCTLIST=CHARACTER ARRAY(25)
 I=NUMBER
 J=NUMBER
 K=NUMBER
 R=NUMBER
 X=NUMBER
 NL=CHARACTER
 MATCHSSNCOUNT=NUMBER
 LOOKFOR=CHARACTER
 MEMNAMES=CHARACTER ARRAY(25)
 MEMSSN=CHARACTER ARRAY(25)
 CHOICES=NUMBER ARRAY(25)
 JOINTCHOICES=NUMBER ARRAY(25)
 MEMCHANGE=NUMBER ARRAY(25)
 NAMELOC=NUMBER ARRAY(25) [STORES THE LOCATOR OF THE NAME ON THE RESPECTIVE ACCOUNT]
 JOINTNAMELOC=NUMBER ARRAY(25)
 MEMTYPE=NUMBER ARRAY(25)    [STORES THE NAME TYPE; PRIMARILY USED TO SEE IF IT'S A MAILING NAME THAT WAS SELECTED]
 WRONGTYPE=NUMBER
 NAMEFOUND=NUMBER
 NAMEADDEDACC=NUMBER
[NAMEONACC=NUMBER]
 MAILLOC=NUMBER
 NAMECLASSES=CHARACTER ARRAY(99)
 FMLIST=CHARACTER ARRAY(25)
 TEMPCHAR=CHARACTER
 ACCLOSED=NUMBER
 TOTNAMES=NUMBER
 NAMESCHOSEN=NUMBER
 NAMECHOSEN=NUMBER
 MAILCHOSEN=NUMBER
 MAILSSN=CHARACTER
 MAILDELETE=NUMBER [0 = NO, 1 = YES; TO TELL THE DIFFERENCE BETWEEN A WRONG TYPE ARRAY
                                     ADJUSTMENT AND A NAME NOT FOUND ARRAY ADJUSTMENT]
 NEWNAME=NUMBER
 MAILING=NUMBER
 FMLIST1=CHARACTER
 FMLIST2=CHARACTER
 FMLIST3=CHARACTER
 MAILSELECT=NUMBER
 
 [VARIABLES TO STORE NEW DATA]
 NEWSTREET=CHARACTER
 NEWCITY  =CHARACTER
 NEWSTATE =CHARACTER
 NEWZIP   =CHARACTER
 NEWHOMEPH=CHARACTER
 NEWMOBPH =CHARACTER
 NEWWORKPH=CHARACTER
 NEWEMAIL =CHARACTER
 
 [VARIABLES TO STORE EXISTING DATA]
 OLDSTREET=CHARACTER ARRAY(25)
 OLDCITY  =CHARACTER ARRAY(25)
 OLDSTATE =CHARACTER ARRAY(25)
 OLDZIP   =CHARACTER ARRAY(25)
 OLDHOMEPH=CHARACTER ARRAY(25)
 OLDMOBPH =CHARACTER ARRAY(25)
 OLDWORKPH=CHARACTER ARRAY(25)
 OLDEMAIL =CHARACTER ARRAY(25)
 
 [DUMMY VARIABLES FOR SAKE OF COLUMNS]
 DUMSTREET=CHARACTER
 DUMCITY  =CHARACTER
 DUMSTATE =CHARACTER
 DUMZIP   =CHARACTER
 DUMHOMEPH=CHARACTER
 DUMMOBPH =CHARACTER
 DUMWORKPH=CHARACTER
 DUMEMAIL =CHARACTER
 
 PHSOK=NUMBER ["PHONE NUMBERS OK", USED TO SEE IF ANY OF THE PHONE NUMBERS ARE INVALID]
 EMAILOK=NUMBER
 TEMPPH=CHARACTER [TO STORE THE PHONE NUMBER THE USER TYPED IN]
 
 EFFDATE=DATE [EFFECTIVE DATE FOR MAILING ONLY ADDRESSES]
 EXPDATE=DATE [EXPIRATION DATE FOR MAILING ONLY ADDRESSES]
 FMERROR=CHARACTER
 IGNORENAME=NUMBER ARRAY(25)
 
 [ID VARIABLES ADDED 12/11/15]
 IDTYPE=CHARACTER
 
 [ADDED 4/29/2020]
 AMPERSANDPOS=NUMBER
 DIALOGUEBOXNAMES=CHARACTER ARRAY(25)
 
 PARENTACCOUNT=CHARACTER
 TEMPNAME=CHARACTER ARRAY(25)
END

SETUP
                              [ PRINT OPTIONS            ]
 PRINTERNAME   = ""           [ PRINTER NAME (OPTIONAL)  ]

 PRINTCOPIES   = 1            [ 0 = ONE COPY (DEFAULT)   ]
                              [ 1 = ONE COPY             ]
                              [ n = n COPIES             ]

 PRINTMODE     = 0            [ 0 = FULL (DEFAULT)       ]
                              [ 1 = AUTOMATIC            ]
                              [ 2 = SILENT               ]

 PRINTANDTHEN  = 0            [ 0 = DO NOTHING (DEFAULT) ]
                              [ 1 = SUBMIT FORM          ]
                              [ 2 = CLOSE FORM           ]

 PRINTDUPLEX   = 0            [ 0 = DEFAULT (DEFAULT)    ]
                              [ 1 = SIMPLEX              ]
                              [ 2 = DUPLEX LONG EDGE     ]
                              [ 3 = DUPLEX SHORT EDGE    ]

 PRINTCONTENTS = 0            [ 0 = DOC (DEFAULT)        ]
                              [ 1 = DOC AND COMMENTS     ]
                              [ 2 = FORM FIELDS ONLY     ]

 NL=CTRLCHR(13)
 PARENTACCOUNT=ACCOUNT:NUMBER
 [CALL CHECKACCTTYPE [ONLY ALLOW GENERAL MEMBERSHIP ACCTS]]
 CALL SETNAMECLASS
 CALL GETMEMNAME
 CALL GETIDSOURCE
 CALL GETADDRESS
 J=1
 FOR ACCOUNT WITH SSN LOOKFOR
  DO
   IF J=26 THEN [TERMINATE THE PROGRAM IF MORE THAN 25 ACCOUNTS EXIST SO THE USER DOESN'T EXPERIENCE OTHER ARRAY-RELATED ERRORS]
    DO
     POPUPMESSAGE(0,"There are more than 25 accounts associated with this SSN.  Please use the EMA revision drop down to complete any address changes.")
     TERMINATE
    END
   [POPUPMESSAGE(0,"J="+FORMAT("#9",J)+", ACCT #= "+ACCOUNT:NUMBER+", NAME LOC = "+FORMAT("####9",NAME:LOCATOR))]
   IF ACCOUNT:NUMBER<>"**********" THEN [ACCT 24 WILL CAUSE A WHILE LOOP ERROR BECAUSE IT HAS SO MANY NAME RECORDS ON IT]
    DO
     ACCTLIST(J)=ACCOUNT:NUMBER
     FMLIST(J)=ACCOUNT:NUMBER
     J=J+1
    END
  END
 MATCHSSNCOUNT=J-1
 NAMESCHOSEN=MATCHSSNCOUNT[FOR PURPOSES OF DISPLAYING ACCT NUMBERS ON FORM, AS MATCHSSNCOUNT IS POTENTIALLY CHANGED DUE TO MAILING OR NOT]
 CALL POPULATELIST

 FORMNAME = "PDF.EPISYSCHGADDRESS.119"
 CALL FORMSETUP
 CLOSEENABLED=0
 
 [ NOTE: if you don't want to load previous saved forms 
   set the following LOADSAVEDFORMS flag to FALSE ]
 LOADSAVEDFORMS = TRUE
 SAVEDESCRIPTION = FORMNAME
 
 FOR ACCOUNT ACCOUNT:NUMBER
  DO
   CALL DATASETUP
   CALL CUSTOMSETUP
  END

 CLOSEFORM = FALSE
 WHILELIMIT = 100000
 WHILE (CLOSEFORM = FALSE)
  DO
   CALL SHOWFORM
   [CALL CLEARCHECKBOXES]
   CALL GETRESPONSE

   IF (FORMMESSAGE = "SUBMIT") THEN
    DO
     CLOSEFORM = CLOSEAFTERSUBMIT
     IF (DOFORMFMP = TRUE) THEN
      DO
       CALL FORMFMP
       IF (FORMFMPERROR <> "") THEN
        DO
         POPUPMESSAGE(2, "PDF.EPISYSCHGADDRESS.119 FM ERROR: " + FORMFMPERROR)
         CLOSEFORM = FALSE
        END
      END
     CALL FINALIZEFORM
     IF (DOOPTICAL = TRUE AND OPTICALTYPE <> "") THEN
      DO
       CALL STOREOPTICAL
       IF (OPTICALERROR <> "") THEN
        DO
         POPUPMESSAGE(2,"PDF.EPISYSCHGADDRESS.119 OPTICAL ERROR: " + OPTICALERROR)
         CLOSEFORM = FALSE
        END
      END
    END
   FORMVERSION = FORMVERSION + 1
  END
END

PRINT TITLE = "PDF FORM " + FORMNAME
 SUPPRESSNEWLINE
END

[
 This procedure is to initialize custom variables defined on the
 PDF Form file. Any variables not recognized by the 'SymPDF Creator'
 will be declared as a custom CHARACTER variable with the field name.

 Examples:
   MYFIELD = "MYVALUE"
   MYFIELD = FORMAT("99/99/9999",SYSTEMDATE)
]
PROCEDURE CUSTOMSETUP
 CALL CUSTOMJAVASCRIPT
 CALL EMAILSETUP
 
 MemberName=SEGMENT(MEMNAMES(NAMECHOSEN),5,LENGTH(MEMNAMES(NAMECHOSEN)))
 I=1
 FOR R=1 TO 25 [25 IS MAX NUMBER OF ACCOUNTS THAT COULD HAVE BEEN CHOSEN]
  DO
   IF CHOICES(R)=1 THEN
    DO
     FMLIST(I)=FMLIST(R)
     I=I+1
    END
  END
 K=I-1
 
 [BLANK OUT THE EXTRA FMLIST ENTRIES THAT WERE NOT SELECTED]
 FOR R=1 TO 25
  DO
   IF R>K THEN FMLIST(R)=""
  END

 FOR R=1 TO NAMESCHOSEN
  DO
   IF R=1 OR R=2 OR R=3 THEN
    DO
     FMLIST1=FMLIST1+FMLIST(R)
     IF R=1 AND FMLIST(2)<>"" AND FMLIST(1)<>"" THEN FMLIST1=FMLIST1+", "
     IF R=2 AND FMLIST(3)<>"" AND FMLIST(2)<>"" THEN FMLIST1=FMLIST1+", "
     IF R=3 AND FMLIST(4)<>"" THEN FMLIST1=FMLIST1+", "
     AccountNumbers1=FMLIST1
    END
   IF R>3 AND R<10 THEN
    DO
     FMLIST2=FMLIST2+FMLIST(R)
     IF R=4 AND FMLIST(5)<>"" THEN FMLIST2=FMLIST2+", "
     IF R=5 AND FMLIST(6)<>"" THEN FMLIST2=FMLIST2+", "
     IF R=6 AND FMLIST(7)<>"" THEN FMLIST2=FMLIST2+", "
     IF R=7 AND FMLIST(8)<>"" THEN FMLIST2=FMLIST2+", "
     IF R=8 AND FMLIST(9)<>"" THEN FMLIST2=FMLIST2+", "
     IF R=9 AND FMLIST(10)<>"" THEN FMLIST2=FMLIST2+", "
     AccountNumbers2=FMLIST2
    END
   IF R>9 AND R<16 THEN
    DO
     FMLIST3=FMLIST3+FMLIST(R)
     IF R=10 AND FMLIST(11)<>"" THEN FMLIST3=FMLIST3+", "
     IF R=11 AND FMLIST(12)<>"" THEN FMLIST3=FMLIST3+", "
     IF R=12 AND FMLIST(13)<>"" THEN FMLIST3=FMLIST3+", "
     IF R=13 AND FMLIST(14)<>"" THEN FMLIST3=FMLIST3+", "
     IF R=14 AND FMLIST(15)<>"" THEN FMLIST3=FMLIST3+", "
     IF R=15 AND FMLIST(16)<>"" THEN FMLIST3=FMLIST3+" (ONLY 15 ACCOUNTS LIST HERE, BUT "+FORMAT("#9",NAMESCHOSEN-15)+" MORE ACCOUNT(S) WERE CHANGED)"
     AccountNumbers3=FMLIST3
    END
  END

 [DETERMINE IF THE PHYSICAL ADDRESS OR MAILING ADDRESS SHOULD BE POPULATED]
 IF MAILSELECT=0 AND MAILING<>3 THEN
  DO
   NewStreetAddress=UPPERCASE(NEWSTREET)
   NewCity1=UPPERCASE(NEWCITY)
   NewState1=UPPERCASE(NEWSTATE)
   NewZIPCode=UPPERCASE(NEWZIP)
  END
 ELSE
  DO
   NewPOBox=UPPERCASE(NEWSTREET)
   PONewCity=UPPERCASE(NEWCITY)
   PONewState=UPPERCASE(NEWSTATE)
   PONewZIPCode=NEWZIP
   ExpirationDate=EXPDATE
  END
 
 NewHomePhone=NEWHOMEPH
 NewWorkPhone=NEWWORKPH
 NewCellPhone=NEWMOBPH
 EmailAddress=NEWEMAIL
 SYSUSER=FORMAT("###9",SYSUSERNUMBER)+" - "+SYSUSERNAME(SYSUSERNUMBER)
 [IF IDTYPE="Driver's License" THEN IDSOURCE="Member ID'd by "+IDTYPE+" ("+DLNUMBER+", expires "+FORMAT("99/99/9999",DLEXPDATE)+")"
  ELSE IDSOURCE="Member ID'd by "+IDTYPE]
 IDSOURCE="Member ID'd by "+IDTYPE
END

[
 This procedure is to override data pased in the PDF.EPISYSCHGADDRESS.119.PRO,
 specifically in the WRITEFORMDATA Procedure. Commands here must
 be in the same format as in the WRITEFORMDATA Procedure.
 This is used for customization since most times you can
 (if possible) set the values in the CUSTOMSETUP procedure above.

 Examples:
   FIELDNAME = "MYFIELD" CHARACTERDATA = "Hello SymFormPDF" CALL WRITECHARACTER
   FIELDNAME = "MYFIELD" MONEYDATA = $3.14 CALL WRITEMONEY
]
PROCEDURE CUSTOMWRITEFORMDATA
END

[
 This procedure is to override data read back from the PDF in the
 PDF.EPISYSCHGADDRESS.119.PRO, specifically in the READFORMDATA Procedure.
 Commands here can be used to do some special reading of variables
 most times this is not used.
]
PROCEDURE CUSTOMREADFORMDATA
END

[
 This procedure is to send special JavaScript code to the PDF from.
 The code specified here will be executed after the field data is
 loaded into the fields. You can declare object variables that are
 used in your form. For example, you could pass the entire Account
 tree to be used in your javascript functions.

 Example:
   JSTEXT = "app.alert('Hello SymFormPDF JavaScript!');" CALL WRITEJS
]
PROCEDURE CUSTOMJAVASCRIPT
END

[
 This procedure is to enable email and set up email for the PDF form.
 If you set the EMAILFORMAT to HTML, you can use html markup in tne 
 email message body.

  EMAILENABLED    0 = FALSE
                  1 = TRUE
  EMAILFORMAT     0 = Plain Text
                  1 = HTML
  EMAILFROM       This is required if EMAILENABLED = TRUE
                  Example: "<EMAIL>"

 Example:
   EMAILTEXT = "This is the email body message" CALL WRITEEMAIL
]
PROCEDURE EMAILSETUP
 EMAILENABLED = 0
 EMAILFORMAT = 0
 EMAILFROM = ""
 EMAILSUBJECT = ""
END

PROCEDURE CHECKACCTTYPE
 IF ACCOUNT:TYPE<>0 THEN
  DO
   POPUPMESSAGE(2,"This program is only functional for general membership accounts. Please use EMA.")
   TERMINATE
  END
END

PROCEDURE GETMEMNAME
 I=1
 FOR EACH NAME WITH (NAME:SSN<>"")
  DO
   IF I=26 THEN [TERMINATE THE PROGRAM IF MORE THAN 25 NAMES EXIST SO THE USER DOESN'T EXPERIENCE OTHER ARRAY-RELATED ERRORS]
    DO
     POPUPMESSAGE(0,"There are more than 25 name records on this account.  Please use the EMA revision drop down to complete any address changes.")
     TERMINATE
    END
[DEFINE MEMBER NAME ARRAY, BUT WITH A NUMBER IN FRONT TO UNIQUELY IDENTIFY THE SAME NAME MULTIPLE TIMES PER ACCT]
 MEMNAMES(I)=FORMAT("#9",I)+". "+NAME:LONGNAME+NAMECLASSES(NAME:TYPE)
 JOINTNAMELOC(I)=NAME:LOCATOR  [STORE NAME LOCATOR FOR FM]
[ADDED 4/29/2020 - SYMITAR DIALOGUE BOXES DO NOT PROPERLY HANDLE AMPERSANDS, SO WE MUST REMOVE THEM FOR USE IN ANY DIALOGUE PROMPTS]
[ADDED 5/2/2022 - ADDED TEMPNAME TO NOT DISRUPT MEMNAMES IN OTHER PARTS IF THE CODE AND A WHILE LOOP IN CASE THERE ARE MORE THAN ONE AMPERSAND PER NAME] 
 AMPERSANDPOS=0
 TEMPNAME(I)=MEMNAMES(I)
 AMPERSANDPOS=CHARACTERSEARCH(TEMPNAME(I),"&")
 WHILE AMPERSANDPOS>0
  DO
   TEMPNAME(I)=SEGMENT(TEMPNAME(I),1,AMPERSANDPOS-1)+" "+SEGMENT(TEMPNAME(I),AMPERSANDPOS+1,LENGTH(TEMPNAME(I)))
   AMPERSANDPOS=CHARACTERSEARCH(TEMPNAME(I),"&")
  END
   DIALOGUEBOXNAMES(I)=TEMPNAME(I)    
 
[DEFINE CURRENT ADDRESS INFO TO POPULATE LEFT SIDE OF ADDRESS CHANGE SCREEN]
   MEMSSN(I)=NAME:SSN
   OLDSTREET(I)=NAME:STREET
   OLDCITY(I)=NAME:CITY
   OLDSTATE(I)=NAME:STATE
   OLDZIP(I)=NAME:ZIPCODE
   OLDHOMEPH(I)=NAME:HOMEPHONE
   OLDMOBPH(I)=NAME:MOBILEPHONE
   OLDWORKPH(I)=NAME:WORKPHONE
   OLDEMAIL(I)=NAME:EMAIL
   MEMTYPE(I)=NAME:TYPE   
   I=I+1   
  END

 TOTNAMES=I-1
 NAMESCHOSEN=0
 WHILE NAMESCHOSEN<>1
  DO
   NAMESCHOSEN=0
   DIALOGSTART("Member Names",100%,0)
   DIALOGINTROTEXT("Please choose which member you'd like to change the address for.")
   DIALOGINTROTEXT("Note that name records without SSN's will NOT appear here.")
 [DISPLAY EACH NAME ALONG WITH A YES/NO DROP DOWN, DEFAULTED TO YES, FOR EACH NAME ON THE ACCOUNT]
   FOR I=1 TO TOTNAMES
    DO
     DIALOGPROMPTCOMBOSTART(SEGMENT(DIALOGUEBOXNAMES(I),1,40),2)
     DIALOGPROMPTCOMBOOPTION(1,"Yes")
     DIALOGPROMPTCOMBOOPTION(2,"No")
     DIALOGPROMPTCOMBOEND
    END
   DIALOGDISPLAY

   FOR I=1 TO TOTNAMES
    DO
     MEMCHANGE(I)=ENTERNUMBER(SEGMENT(DIALOGUEBOXNAMES(I),1,40),2)
    END
   DIALOGCLOSE

   MAILSELECT=0
   FOR I=1 TO TOTNAMES
    DO
     IF MEMCHANGE(I)=1 THEN
      DO
       NAMESCHOSEN=NAMESCHOSEN+1
       LOOKFOR=MEMSSN(I)
       NAMECHOSEN=I
      END
     IF MEMTYPE(I)=2 OR MEMTYPE(I)=3 THEN 
      DO
       MAILCHOSEN=I
       MAILSSN=MEMSSN(I)
      END
     IF MEMTYPE(NAMECHOSEN)=2 OR MEMTYPE(NAMECHOSEN)=3 THEN MAILSELECT=1
    END

   IF NAMESCHOSEN<>1 THEN
    POPUPMESSAGE(2,"Please choose exactly one member!")
  END
END

PROCEDURE POPULATELIST
 IF DEBUGLEVEL>0 THEN
  DO
   POPUPMESSAGE(0,"aMT = "+FORMAT("#9",MATCHSSNCOUNT)+NL+NL+
                "A(1) = "+ACCTLIST(1)+NL+
                "A(2) = "+ACCTLIST(2)+NL+
                "A(3) = "+ACCTLIST(3)+NL+
                "A(4) = "+ACCTLIST(4)+NL+
                "A(5) = "+ACCTLIST(5)+NL+
                "A(6) = "+ACCTLIST(6)+NL+
                "A(7) = "+ACCTLIST(7)+NL)
  END
 FOR J=1 TO MATCHSSNCOUNT
  DO
   ACCLOSED=0
   NAMEADDEDACC=0
   FOR ACCOUNT ACCTLIST(J)
    DO
     IF DEBUGLEVEL>0 THEN
      DO
       POPUPMESSAGE(0,"ENTERING ACCOUNT"+NL+NL+
                      "J= "+FORMAT("#9",J)+NL+
                      "ACCT # = "+ACCOUNT:NUMBER)
      END
     IF ACCOUNT:CLOSEDATE<>'--/--/--' THEN ACCLOSED=1
     NEWNAME=0
     NAMEFOUND=0
     FOR EACH NAME
      DO
       WRONGTYPE=0
       IF DEBUGLEVEL>1 THEN
        DO
         POPUPMESSAGE(0,"ENTERING NAME"+NL+NL+
                        "J= "+FORMAT("#9",J)+NL+
                        "NAME TYPE = "+FORMAT("#9",NAME:TYPE)+NL+
                        "NAME LOCATOR = "+FORMAT("####9",NAME:LOCATOR)+NL+
                        "NAME SSN = "+NAME:SSN+NL+
                        "LOOKFOR SSN = "+LOOKFOR+NL+
                        "NAME = "+NAME:SHORTNAME)
        END
       [DETERMINE WHAT TYPES OF NAMES SHOULD BE EXCLUDED BASED ON MAILSELECT]
       MAILDELETE=0
       IF MAILSELECT=0 THEN
        DO
         IF (NAME:TYPE=2 OR NAME:TYPE=3) AND NAME:SSN=LOOKFOR THEN
          DO
           MAILDELETE=1
           CALL ADJUSTARRAY
          END
        END
       ELSE
        DO
         IF NAME:TYPE<>2 AND NAME:TYPE<>3 AND NAME:SSN=LOOKFOR THEN
          DO
           MAILDELETE=1
           CALL ADJUSTARRAY
          END
        END
       [MAKE SURE THE NAME HASN'T ALREADY BEEN MODIFIED WITH THE ACCT CLOSED AND/OR THE NAME CLASS]
       IF NAME:SSN=LOOKFOR AND WRONGTYPE=0 THEN
        DO
         NAMEFOUND=NAMEFOUND+1
         NAMEADDEDACC=NAMEADDEDACC+1
         IF DEBUGLEVEL>1 THEN
          DO
           POPUPMESSAGE(0,"NAME OK"+NL+NL+
                          "J= "+FORMAT("#9",J)+NL+
                          "NAME TYPE = "+FORMAT("#9",NAME:TYPE)+NL+
                          "NAME LOCATOR = "+FORMAT("####9",NAME:LOCATOR)+NL+
                          "NAME = "+NAME:SHORTNAME)
          END
         IF NAMELOC(J+NEWNAME)=0 THEN
          DO
           NAMELOC(J+NEWNAME)=NAME:LOCATOR
           IF DEBUGLEVEL>2 THEN POPUPMESSAGE(0,FORMAT("99",J+NEWNAME))
           NEWNAME=NEWNAME+1
          END
        END
      END
    [TAKE OUT ALL INSTANCES OF SHARE, LOAN, OR CARD NAMES BEING FOUND, AS THESE ARE IGNORED]
    [ONLY DO THIS IF THE NAME RECORD EXISTS ON THE ACCOUNT LEVEL IN THE FIRST PLACE]
     IF DEBUGLEVEL>0 AND NAMEFOUND=0 THEN POPUPMESSAGE(0,"NO NAME FOUND, ACCT # "+ACCOUNT:NUMBER)
     [NAMEONACC=NAMEFOUND]   
     IF NAMEFOUND>0 THEN
      DO
       IF DEBUGLEVEL>1 THEN POPUPMESSAGE(0,"NAMEFOUND>0")
       FOR EACH SHARE
        DO
         FOR EACH SHARE NAME WITH (SHARE NAME:SSN=LOOKFOR)
          DO
           CALL ADJUSTARRAY
           [NAMEADDEDACC=NAMEADDEDACC+1]
          END
        END
       FOR EACH LOAN
        DO
         FOR EACH LOAN NAME WITH (LOAN NAME:SSN=LOOKFOR)
          DO
           CALL ADJUSTARRAY
           [NAMEADDEDACC=NAMEADDEDACC+1]
          END
         FOR EACH LOAN PLEDGE
          DO
           FOR EACH LOAN PLEDGE NAME WITH (LOAN PLEDGE NAME:SSN=LOOKFOR)
            DO
             CALL ADJUSTARRAY
             [NAMEADDEDACC=NAMEADDEDACC+1]
            END
          END
        END
       FOR EACH CARD
        DO
         FOR EACH CARD NAME WITH (CARD NAME:SSN=LOOKFOR)
          DO
           CALL ADJUSTARRAY
           [NAMEADDEDACC=NAMEADDEDACC+1]
          END
        END
       FOR EACH IRS
        DO
         FOR EACH IRS NAME WITH (IRS NAME:SSN=LOOKFOR)
          DO
           CALL ADJUSTARRAY
           [NAMEADDEDACC=NAMEADDEDACC+1]
          END
        END
       FOR EACH EFT
        DO
         FOR EACH EFT NAME WITH (EFT NAME:SSN=LOOKFOR)
          DO
           CALL ADJUSTARRAY
           [NAMEADDEDACC=NAMEADDEDACC+1]
          END
        END
      END
     ELSE CALL ADJUSTARRAY

     [IF AT LEAST ONE VALID NAME WAS FOUND ON THIS ACCOUNT, ADD IT TO THE ARRAY]
     K=0
     IF NAMEFOUND>0 THEN
      DO
       FOR EACH NAME WITH (NAME:LOCATOR=NAMELOC(J+K))
        DO
         TEMPCHAR=ACCTLIST(J+K)
         CALL REMOVEZERO
         IF TEMPCHAR<>"" THEN
          DO
           ACCTLIST(J+K)=TEMPCHAR
           ACCTLIST(J+K)=ACCTLIST(J+K)+NAMECLASSES(NAME:TYPE)         
           IF ACCLOSED=1 THEN ACCTLIST(J+K)=ACCTLIST(J+K)+" (ACCT CLOSED)"
           IGNORENAME(J+K)=0
           IF NAME:TYPE=4 THEN
            DO
             [IGNORENAME(J+K)=1]
             IF MAILSELECT=0 THEN CHOICES(J+K)=1
             IF MAILSELECT=1 THEN CHOICES(J+K)=0
            END
           NAMELOC(J+K)=NAME:LOCATOR
           FMLIST(J+K)=ACCOUNT:NUMBER
           NAMEFOUND=NAMEFOUND-1
           K=K+1
           IF DEBUGLEVEL>2 THEN
            DO
             POPUPMESSAGE(0,"NAME ADDED TO ARRAY"+NL+NL+
               "EFFECTIVE J= "+FORMAT("#9",J+K)+NL+
               "NAME TYPE = "+FORMAT("#9",NAME:TYPE)+NL+
               "ACCT # = "+ACCOUNT:NUMBER+NL+
               "NAMELOC(J) = "+FORMAT("####9",NAMELOC(J+K))+NL+
               "NAME LOCATOR = "+FORMAT("####9",NAME:LOCATOR))
            END
          END
        END        
        X=X+1       
      END
     IF DEBUGLEVEL>2 AND WHILECOUNTER<20 THEN POPUPMESSAGE(0,"BEFORE J="+FORMAT(" +#9",J))
     J=J+NEWNAME-1
     IF DEBUGLEVEL>2 AND WHILECOUNTER<20 THEN POPUPMESSAGE(0,"AFTER J="+FORMAT(" +#9",J))
    END
   WHILECOUNTER=WHILECOUNTER+1
   IF DEBUGLEVEL>1 AND WHILECOUNTER<20 THEN POPUPMESSAGE(0,"BEFORE 2 J="+FORMAT(" +#9",J))
   IF NAMEADDEDACC=0 AND WRONGTYPE=1 THEN J=J-1
   IF DEBUGLEVEL>1 AND WHILECOUNTER<20 THEN POPUPMESSAGE(0,"AFTER 2 J="+FORMAT(" +#9",J))
  END
 J=J-1

 DIALOGSTART("Acct List",100%,1)
 DIALOGINTROTEXT("("+SEGMENT(MEMNAMES(NAMECHOSEN),5,LENGTH(MEMNAMES(NAMECHOSEN)))+")")
 DIALOGINTROTEXT("Please choose which account(s) to apply changes to.")
 FOR K=1 TO X+1
  DO
   IF ACCTLIST(K)<>"" THEN
    DO
     DIALOGPROMPTCOMBOSTART(SEGMENT(ACCTLIST(K),1,40),1)
     DIALOGPROMPTCOMBOOPTION(1,"Yes")
     DIALOGPROMPTCOMBOOPTION(2,"No")
     DIALOGPROMPTCOMBOEND
    END
  END
  
 [JEFF H ADDED 7/15/2020 - SHOW OTHER NAMES ON THIS ACCOUNT TO MODIFY ADDRESS]
 FOR K=1 TO TOTNAMES
  DO
   IF MEMSSN(K)<>LOOKFOR THEN
    DO
     DIALOGSTARTGROUPBOX("Please choose joint names on this account to apply changes to.")
     K=TOTNAMES+1
    END
  END
 FOR K=1 TO TOTNAMES
  DO
   IF MEMSSN(K)<>LOOKFOR THEN
    DO
     DIALOGPROMPTCOMBOSTART(SEGMENT(DIALOGUEBOXNAMES(K),4,40),2)
     DIALOGPROMPTCOMBOOPTION(1,"Yes")
     DIALOGPROMPTCOMBOOPTION(2,"No")
     DIALOGPROMPTCOMBOEND
    END
  END
DIALOGENDGROUPBOX
DIALOGDISPLAY

 FOR K=1 TO X+1
  DO
   IF ACCTLIST(K)<>"" THEN
    DO
     CHOICES(K)=ENTERNUMBER(SEGMENT(ACCTLIST(K),1,40),1)
    END
  END
 FOR K=1 TO TOTNAMES
  DO
   IF MEMSSN(K)<>LOOKFOR THEN
    DO
     JOINTCHOICES(K)=ENTERNUMBER(SEGMENT(DIALOGUEBOXNAMES(K),4,40),1)
    END
  END
 DIALOGCLOSE
 [K=K-1]
 FOR K = 1 TO X+1
  DO
   IF CHOICES(K)=1 THEN
    DO
     CALL PERFORMFM
    END
  END
 FOR K = 1 TO TOTNAMES
  DO
   IF JOINTCHOICES(K)=1 THEN
    DO
     CALL PERFORMJOINTFM
    END
  END
END

PROCEDURE GETADDRESS
 IF MAILSELECT=0 THEN
  DO
   MAILING=1
   WHILE MAILING=1 OR MAILING=2
    DO
     DIALOGSTART(SEGMENT(MEMNAMES(NAMECHOSEN),5,LENGTH(MEMNAMES(NAMECHOSEN)))+" - Mailing Address?",100%,0)
     DIALOGINTROTEXT("Is this a mailing only address or physical address?")   
     DIALOGPROMPTCOMBOSTART("Option",4)
     DIALOGPROMPTCOMBOOPTION(1,"Please choose from below")
     DIALOGPROMPTCOMBOOPTION(2,"------------")
     DIALOGPROMPTCOMBOOPTION(3,"Mailing Only")
     DIALOGPROMPTCOMBOOPTION(4,"Physical")
     DIALOGPROMPTCOMBOEND
     DIALOGDISPLAY

     MAILING=ENTERCODE("Option",4,4)
     DIALOGCLOSE

     IF MAILING=1 OR MAILING=2 THEN
      POPUPMESSAGE(2,"Please choose an option!")
    END

   IF MAILING=3 THEN CALL MAILSELECTED
   IF MAILING=4 THEN CALL GETPHYSADDR
  END
 ELSE 
  DO
   CALL GETMAILINFO
   CALL GETMAILADDR
  END
END

PROCEDURE MAILSELECTED
 CALL GETMAILINFO
 IF MAILLOC<>0 THEN
  DO
   NEWNAME=1
   WHILE NEWNAME=1 OR NEWNAME=2
    DO
     DIALOGSTART("Use Existing Mailing Address?",100%,0)
     IF MAILSSN=LOOKFOR THEN
      DIALOGINTROTEXT("A mailing only address already exists for the SSN that you picked.  Would you like to modify it?")
     ELSE
      DIALOGINTROTEXT("A mailing only address already exists, but for a different SSN than the one that you picked.  Would you like to modify it?")
     DIALOGPROMPTCOMBOSTART("Option",1)
     DIALOGPROMPTCOMBOOPTION(1,"Please choose from below")
     DIALOGPROMPTCOMBOOPTION(2,"------------")
     DIALOGPROMPTCOMBOOPTION(3,"Modify Existing Record")
     DIALOGPROMPTCOMBOOPTION(4,"Cancel and Start Over")
     DIALOGPROMPTCOMBOEND
     DIALOGDISPLAY

     NEWNAME=ENTERCODE("Option",4,1)
     DIALOGCLOSE

     IF NEWNAME=1 OR NEWNAME=2 THEN
      POPUPMESSAGE(2,"Please choose an option!")
    END
   IF NEWNAME=3 THEN
    DO
     MAILSELECT=1
     NAMECHOSEN=MAILCHOSEN
     LOOKFOR=MAILSSN
     IF DEBUGLEVEL>2 THEN POPUPMESSAGE(0,"CHANGE NAMECHOSEN")
    END
   IF NEWNAME=4 THEN TERMINATE
  END
 CALL GETMAILADDR
END

PROCEDURE GETMAILADDR
 EFFDATE='1/2/00'
 EXPDATE='1/1/00'
 WHILE (EFFDATE>EXPDATE) AND EXPDATE<>'-/-/-'
  DO
   DIALOGSTART("Mailing Dates",100%,0)
   DIALOGINTROTEXT("Please enter the effective date and expiration date for the mailing only address.")
   DIALOGPROMPTDATE("Effective Date",'--/--/--')
   DIALOGPROMPTDATE("Expiration Date",'--/--/--')
   DIALOGDISPLAY
   EFFDATE=ENTERDATE("Effective Date",'--/--/--')
   EXPDATE=ENTERDATE("Expiration Date",'--/--/--')
   DIALOGCLOSE

   IF (EFFDATE>EXPDATE) AND EXPDATE<>'-/-/-' THEN
    POPUPMESSAGE(2,"The effective date can not be after the expiration date!")
  END
 CALL GETPHYSADDR
END

PROCEDURE GETPHYSADDR
 IF MAILING=4 THEN
  DO 
   EFFDATE='--/--/--'
   EXPDATE='--/--/--'
  END
 
 DUMSTREET=OLDSTREET(NAMECHOSEN)
 DUMCITY  =OLDCITY(NAMECHOSEN)
 DUMSTATE =OLDSTATE(NAMECHOSEN)
 DUMZIP   =SEGMENT(OLDZIP(NAMECHOSEN),1,5)
 DUMHOMEPH=OLDHOMEPH(NAMECHOSEN)
 DUMMOBPH =OLDMOBPH(NAMECHOSEN)
 DUMWORKPH=OLDWORKPH(NAMECHOSEN)
 DUMEMAIL =OLDEMAIL(NAMECHOSEN)
 NEWSTREET=DUMSTREET
 NEWCITY=DUMCITY
 NEWSTATE=DUMSTATE
 NEWZIP=DUMZIP
 NEWHOMEPH=DUMHOMEPH
 NEWMOBPH=DUMMOBPH
 NEWWORKPH=DUMWORKPH
 NEWEMAIL=DUMEMAIL
 PHSOK=0
 EMAILOK=0
 WHILE NEWSTREET="" OR
       NEWCITY="" OR
       LENGTH(NEWZIP)<>5 OR
       LENGTH(NEWSTATE)<>2 OR
       PHSOK=0 OR
       EMAILOK=0 OR
      (MAILING=4 AND
      (CHARACTERSEARCH(UPPERCASE(NEWSTREET),"PO BOX")=1 OR
       CHARACTERSEARCH(UPPERCASE(NEWSTREET),"P.O. BOX")=1 OR
       CHARACTERSEARCH(UPPERCASE(NEWSTREET),"P O BOX")=1 OR
       CHARACTERSEARCH(UPPERCASE(NEWSTREET),"POST OFFICE")=1 OR
       CHARACTERSEARCH(UPPERCASE(NEWSTREET),"POBOX")=1))

  DO
  [DISPLAY MEMBER NAME AS THE DIALOG BOX TITLE, BUT WITHOUT THE NUMBER AND PERIOD USED TO UNIQUELY IDENTIFY THEM PREVIOUSLY]
   DIALOGSTART(SEGMENT(MEMNAMES(NAMECHOSEN),5,LENGTH(MEMNAMES(NAMECHOSEN)))+" Information",100%,0)
   DIALOGSTARTGROUPBOX("Current Information")
   DIALOGPROMPTCHAR("Street",40,DUMSTREET)
   DIALOGPROMPTCHAR("City",40,DUMCITY)
   DIALOGPROMPTCHAR("State",2,DUMSTATE)
   DIALOGPROMPTCHAR("Zip",5,DUMZIP)
   DIALOGPROMPTCHAR("Home Phone",12,DUMHOMEPH)
   DIALOGPROMPTCHAR("Mobile Phone",12,DUMMOBPH)
   DIALOGPROMPTCHAR("Work Phone",12,DUMWORKPH)
   DIALOGPROMPTCHAR("Email Address",50,DUMEMAIL)
   DIALOGENDGROUPBOX
   DIALOGNEWCOLUMN
   DIALOGSTARTGROUPBOX("New Information")
   DIALOGPROMPTCHAR("New Street",40,NEWSTREET)
   DIALOGPROMPTCHAR("New City",40,NEWCITY)
   DIALOGPROMPTCHAR("New State",2,NEWSTATE)
   DIALOGPROMPTCHAR("New Zip",5,SEGMENT(NEWZIP,1,5))
   DIALOGPROMPTCHAR("New Home Phone (xxx-xxx-xxxx)",12,NEWHOMEPH)
   DIALOGPROMPTCHAR("New Mobile Phone (xxx-xxx-xxxx)",12,NEWMOBPH)
   DIALOGPROMPTCHAR("New Work Phone (xxx-xxx-xxxx)",12,NEWWORKPH)
   DIALOGPROMPTCHAR("New Email Address",50,NEWEMAIL)
   DIALOGENDGROUPBOX
   
   DIALOGDISPLAY
 
   DUMSTREET=ENTERCHARACTER("Street",40,DUMSTREET)
   DUMCITY  =ENTERCHARACTER("City",40,DUMCITY)
   DUMSTATE =ENTERCHARACTER("State",2,DUMSTATE)
   DUMZIP   =ENTERCHARACTER("Zip",5,DUMZIP)
   DUMHOMEPH=ENTERCHARACTER("Home Phone",12,DUMHOMEPH)
   DUMMOBPH =ENTERCHARACTER("Mobile Phone",12,DUMMOBPH)
   DUMWORKPH=ENTERCHARACTER("Work Phone",12,DUMWORKPH)
   DUMEMAIL =ENTERCHARACTER("Email Address",50,DUMEMAIL)

   NEWSTREET=ENTERCHARACTER("New Street",40,NEWSTREET)
   NEWCITY  =ENTERCHARACTER("New City",40,NEWCITY)
   NEWSTATE =ENTERCHARACTER("New State",2,NEWSTATE)
   NEWZIP   =ENTERCHARACTER("New Zip",5,NEWZIP)
   NEWHOMEPH=ENTERCHARACTER("New Home Phone (xxx-xxx-xxxx)",12,NEWHOMEPH)
   NEWMOBPH =ENTERCHARACTER("New Mobile Phone (xxx-xxx-xxxx)",12,NEWMOBPH)
   NEWWORKPH=ENTERCHARACTER("New Work Phone (xxx-xxx-xxxx)",12,NEWWORKPH)
   NEWEMAIL =ENTERCHARACTER("New Email Address",50,NEWEMAIL)
   DIALOGCLOSE

   IF NEWSTREET="" THEN POPUPMESSAGE(2,"Please enter a street!")
   IF MAILING=4 AND (CHARACTERSEARCH(UPPERCASE(NEWSTREET),"PO BOX")=1 OR
                     CHARACTERSEARCH(UPPERCASE(NEWSTREET),"P.O. BOX")=1 OR
                     CHARACTERSEARCH(UPPERCASE(NEWSTREET),"P O BOX")=1 OR
                     CHARACTERSEARCH(UPPERCASE(NEWSTREET),"POST OFFICE")=1 OR
                     CHARACTERSEARCH(UPPERCASE(NEWSTREET),"POBOX")=1) THEN POPUPMESSAGE(2,"Cannot Be a PO BOX, Please Choose a Physical Address!")     
   IF NEWCITY="" THEN POPUPMESSAGE(2,"Please enter a city!")
   IF LENGTH(NEWSTATE)<>2 THEN POPUPMESSAGE(2,"Please enter a 2 character state!")
   IF LENGTH(NEWZIP)<>5 THEN POPUPMESSAGE(2,"Please enter a 5 digit zipcode!")
   PHSOK=1
   EMAILOK=1
   TEMPPH=NEWZIP
   CALL VALIDATEZIP
   IF PHSOK=0 THEN POPUPMESSAGE(2,"Please only enter numbers for the ZIP code.")   
   CALL VALIDATEEMAIL
   IF EMAILOK=0 THEN POPUPMESSAGE(2,"Please enter a valid email address.")
   CALL VALIDATEPHS
  END 
END

PROCEDURE GETMAILINFO
 MAILLOC=0
 FOR EACH NAME WITH (NAME:TYPE=2 OR NAME:TYPE=3)
  DO
   MAILLOC=NAME:LOCATOR
  END
END

PROCEDURE VALIDATEPHS
 IF NEWHOMEPH<>"" THEN
  DO
   TEMPPH=NEWHOMEPH
   CALL VALIDATEPH
   IF PHSOK=0 THEN POPUPMESSAGE(2,"Please enter a correctly formatted home phone number.")
  END
 IF NEWMOBPH <>"" AND PHSOK=1 THEN
  DO
   TEMPPH=NEWMOBPH
   CALL VALIDATEPH
   IF PHSOK=0 THEN POPUPMESSAGE(2,"Please enter a correctly formatted mobile phone number.")
  END
 IF NEWWORKPH<>"" AND PHSOK=1 THEN
  DO
   TEMPPH=NEWWORKPH
   CALL VALIDATEPH
   IF PHSOK=0 THEN POPUPMESSAGE(2,"Please enter a correctly formatted work phone number.")
  END
END

PROCEDURE VALIDATEPH
 IF CHARACTERSEARCH("**********",SEGMENT(TEMPPH,1,1))=0 THEN PHSOK=0
 IF CHARACTERSEARCH("**********",SEGMENT(TEMPPH,2,2))=0 THEN PHSOK=0
 IF CHARACTERSEARCH("**********",SEGMENT(TEMPPH,3,3))=0 THEN PHSOK=0
 IF CHARACTERSEARCH("-",SEGMENT(TEMPPH,4,4))=0 THEN PHSOK=0
 IF CHARACTERSEARCH("**********",SEGMENT(TEMPPH,5,5))=0 THEN PHSOK=0
 IF CHARACTERSEARCH("**********",SEGMENT(TEMPPH,6,6))=0 THEN PHSOK=0
 IF CHARACTERSEARCH("**********",SEGMENT(TEMPPH,7,7))=0 THEN PHSOK=0
 IF CHARACTERSEARCH("-",SEGMENT(TEMPPH,8,8))=0 THEN PHSOK=0
 IF CHARACTERSEARCH("**********",SEGMENT(TEMPPH,9,9))=0 THEN PHSOK=0
 IF CHARACTERSEARCH("**********",SEGMENT(TEMPPH,10,10))=0 THEN PHSOK=0
 IF CHARACTERSEARCH("**********",SEGMENT(TEMPPH,11,11))=0 THEN PHSOK=0
 IF CHARACTERSEARCH("**********",SEGMENT(TEMPPH,12,12))=0 THEN PHSOK=0
END

PROCEDURE VALIDATEZIP
 IF CHARACTERSEARCH("**********",SEGMENT(TEMPPH,1,1))=0 THEN PHSOK=0
 IF CHARACTERSEARCH("**********",SEGMENT(TEMPPH,2,2))=0 THEN PHSOK=0
 IF CHARACTERSEARCH("**********",SEGMENT(TEMPPH,3,3))=0 THEN PHSOK=0
 IF CHARACTERSEARCH("**********",SEGMENT(TEMPPH,4,4))=0 THEN PHSOK=0
 IF CHARACTERSEARCH("**********",SEGMENT(TEMPPH,5,5))=0 THEN PHSOK=0
END

PROCEDURE VALIDATEEMAIL
 IF NEWEMAIL<>"" THEN
  DO
   IF CHARACTERSEARCH(NEWEMAIL,",")>0 THEN EMAILOK=0
   IF CHARACTERSEARCH(NEWEMAIL,"@")=0 THEN EMAILOK=0
   IF CHARACTERSEARCH(NEWEMAIL,".")=0 THEN EMAILOK=0
  END
END

PROCEDURE PERFORMFM   [NOW WORKS DON'T CHANGE ANYTHING]
 FOR ACCOUNT FMLIST(K)
  DO
   [POPUPMESSAGE(0,"FMLIST(1)="+FMLIST(1)+NL+"FMLIST(2)="+FMLIST(2)+NL+
                  "NAMELOC(1)="+FORMAT("###9",NAMELOC(1))+NL+"NAMELOC(2)="+FORMAT("###9",NAMELOC(2)))]
   INITSUBROUTINE(FMERROR)
   @ENVARGCHAR1=NEWSTREET
   @ENVARGCHAR2=NEWCITY
   @ENVARGCHAR3=NEWSTATE
   @ENVARGCHAR4=NEWZIP
   @ENVARGCHAR5=NEWHOMEPH
   @ENVARGCHAR6=NEWMOBPH
   @ENVARGCHAR7=NEWWORKPH
   @ENVARGCHAR8=NEWEMAIL
   @ENVARGDATE1=EFFDATE
   @ENVARGDATE2=EXPDATE
   @ENVARGNUMBER1=NAMELOC(K)
   @ENVARGNUMBER2=MAILLOC
   @ENVARGNUMBER3=MAILING
   EXECUTE("JK.ADDRFMPERFORM", FMERROR)
   IF FMERROR<>"" THEN POPUPMESSAGE(2,FMERROR)
  END
END

[JEFF H ADDED 7/15/2020 - FM ADDRESS OF OTHER NAMES ON ACCOUNT BUT KEEP THEIR UNIQUE PHONE AND EMAILS]
PROCEDURE PERFORMJOINTFM
 FOR ACCOUNT PARENTACCOUNT
  DO
   INITSUBROUTINE(FMERROR)
   @ENVARGCHAR1=NEWSTREET
   @ENVARGCHAR2=NEWCITY
   @ENVARGCHAR3=NEWSTATE
   @ENVARGCHAR4=NEWZIP
   @ENVARGCHAR5=OLDHOMEPH(K)
   @ENVARGCHAR6=OLDMOBPH(K)
   @ENVARGCHAR7=OLDWORKPH(K)
   @ENVARGCHAR8=OLDEMAIL(K)
   @ENVARGDATE1=EFFDATE
   @ENVARGDATE2=EXPDATE
   @ENVARGNUMBER1=JOINTNAMELOC(K)
   @ENVARGNUMBER2=MAILLOC
   @ENVARGNUMBER3=MAILING
   EXECUTE("JK.ADDRFMPERFORM", FMERROR)
   IF FMERROR<>"" THEN POPUPMESSAGE(2,FMERROR)
  END
END

PROCEDURE SETNAMECLASS
 CALL ECUSETNAMETYPEDESCRIPTIONS
 FOR I=0 TO 99
  DO
   NAMECLASSES(I)=" (" + ECUNAMETYPEDESCRIPTION(I) + ")"
  END
END

PROCEDURE REMOVEZERO
 R = 1
 WHILE SEGMENT(TEMPCHAR,R,R)="0" OR
 SEGMENT(TEMPCHAR,R,R)="," DO
 R = R + 1
 END
 TEMPCHAR = SEGMENT(TEMPCHAR,R,LENGTH(TEMPCHAR))
END

PROCEDURE ADJUSTARRAY
 IF DEBUGLEVEL>1 THEN
  DO
   POPUPMESSAGE(0,"bMATCHSSNCOUNT = "+FORMAT("#9",MATCHSSNCOUNT)+NL+NL+
                "A(1) = "+ACCTLIST(1)+NL+
                "A(2) = "+ACCTLIST(2)+NL+
                "A(3) = "+ACCTLIST(3)+NL+
                "A(4) = "+ACCTLIST(4)+NL+
                "A(5) = "+ACCTLIST(5)+NL+
                "A(6) = "+ACCTLIST(6)+NL)
  END
 FOR R=J TO MATCHSSNCOUNT
  DO
   ACCTLIST(R)=ACCTLIST(R+1)
   IF DEBUGLEVEL>2 THEN
    DO
     POPUPMESSAGE(0,["REMOVE NAME"+NL+NL+
                  "ACCT # = "+ACCOUNT:NUMBER+NL+
                  "R = "+FORMAT("#9",R)+NL+
                  "NAME TYPE = "+FORMAT("#9",NAME:TYPE)+NL+
                  "NAME LOCATOR = "+FORMAT("####9",NAME:LOCATOR)+NL+]
                  "MATCHSSNCOUNT = "+FORMAT("#9",MATCHSSNCOUNT)+NL+NL+
                  "A(1) = "+ACCTLIST(1)+NL+
                  "A(2) = "+ACCTLIST(2)+NL+
                  "A(3) = "+ACCTLIST(3)+NL+
                  "A(4) = "+ACCTLIST(4)+NL+
                  "A(5) = "+ACCTLIST(5)+NL+
                  "A(6) = "+ACCTLIST(6)+NL)
    END
  END
   IF DEBUGLEVEL>2 THEN
    DO
     POPUPMESSAGE(0,["REMOVE NAME"+NL+NL+
                  "ACCT # = "+ACCOUNT:NUMBER+NL+
                  "R = "+FORMAT("#9",R)+NL)]
                  ["NAME TYPE = "+FORMAT("#9",NAME:TYPE)+NL+
                  "NAME LOCATOR = "+FORMAT("####9",NAME:LOCATOR)+NL+
                  "MATCHSSNCOUNT = "+FORMAT("#9",MATCHSSNCOUNT)+NL+NL+]
                  "A(1) = "+ACCTLIST(1)+NL+
                  "A(2) = "+ACCTLIST(2)+NL+
                  "A(3) = "+ACCTLIST(3)+NL+
                  "A(4) = "+ACCTLIST(4)+NL+
                  "A(5) = "+ACCTLIST(5)+NL+
                  "A(6) = "+ACCTLIST(6)+NL+
                  "A(7) = "+ACCTLIST(7)+NL+
                  "A(8) = "+ACCTLIST(8)+NL)
    END
 MATCHSSNCOUNT=MATCHSSNCOUNT-1
 IF MAILDELETE=1 THEN WRONGTYPE=1
END

PROCEDURE GETIDSOURCE
 IF DEBUGLEVEL=0 THEN
  DO
   K=1
   WHILE K=1 OR K=2
    DO
     DIALOGSTART("Member Identification",100%,0)
     DIALOGINTROTEXT("Please select how you're verifying the member's identity?")   
     DIALOGPROMPTCOMBOSTART("Option",1)
     DIALOGPROMPTCOMBOOPTION(1,"Please choose from below")
     DIALOGPROMPTCOMBOOPTION(2,"------------")
     DIALOGPROMPTCOMBOOPTION(3,"Valid ID")
     DIALOGPROMPTCOMBOOPTION(4,"Synergy")
     DIALOGPROMPTCOMBOOPTION(5,"Known")
     DIALOGPROMPTCOMBOEND
     DIALOGDISPLAY
  
     K=ENTERCODE("Option",5,1)
     DIALOGCLOSE
  
     IF K=1 OR K=2 THEN
      POPUPMESSAGE(2,"Please choose an option!")
    END
  
        IF K=3 THEN IDTYPE="Valid ID"
   ELSE IF K=4 THEN IDTYPE="Synergy"
   ELSE IF K=5 THEN IDTYPE="Known"
  END
 ELSE IDTYPE="(Debug Mode)"
 
 [IF K=3 THEN
  DO
   DLNUMBER=""
   DLEXPDATE='-/-/-'
   WHILE DLNUMBER="" OR DLEXPDATE<SYSTEMDATE
    DO
     DIALOGSTART("Driver's License Info",100%,0)
     DIALOGINTROTEXT("Please enter the driver's license number and expiration date.")
     DIALOGPROMPTCHAR("Number",20,DLNUMBER)
     DIALOGPROMPTDATE("Expiration Date",DLEXPDATE)
     DIALOGDISPLAY
  
     DLNUMBER=ENTERCHARACTER("Number",20,DLNUMBER)
     DLEXPDATE=ENTERDATE("Expiration Date",DLEXPDATE)
     DIALOGCLOSE
  
     IF DLNUMBER="" THEN POPUPMESSAGE(2,"Please enter an driver's license number!")
     IF DLEXPDATE='--/--/--' THEN POPUPMESSAGE(2,"Please enter an expiration date!")
     ELSE IF DLEXPDATE<SYSTEMDATE THEN POPUPMESSAGE(2,"Please enter an expiration date that is not in the past!")
    END
  END]
END

#INCLUDE "SYMPDF.PRO"
#INCLUDE "PDF.EPISYSCHGADDRESS.119.PRO"
#INCLUDE "ECU.DESCRIPTIONS.OD.PRO"
